// invite.js
let invitationSummary = { sent: 0, failed: 0 };
let detailsVisible = false;
let sentEmails = [];
let failedEmails = [];
let isPaidUser = false;
let creditListener = null;
let userCredits = 0;
let promptUpdateCount = 0;
let creditPromptCount = 0;
let isUpdatingPrompt = null;
let isEmailVerified = false;
let verificationInterval = null;
let verificationListener = null;
let companyWebLink = 'https://assessment.skillsassess.ai/SGA.html';
let assessmentType = 'digital'; // Default to digital skills
let manualEntryInitialized = false;
let isSubmitting = false;

async function checkEmailVerification() {
    try {
        const currentUser = firebase.auth().currentUser;
        if (!currentUser) {
            console.error('No authenticated user found');
            isEmailVerified = false;
            updatePromptDisplay();
            return false;
        }

        await currentUser.reload();
        const newVerificationStatus = currentUser.emailVerified;

        if (newVerificationStatus !== isEmailVerified) {
            isEmailVerified = newVerificationStatus;
            updatePromptDisplay();
            updateSendButtonState();
        }

        return isEmailVerified;
    } catch (error) {
        console.error('Error checking email verification:', error);
        return false;
    }
}


function cleanupEmailVerificationListener() {
    console.log('Cleaning up verification listener');

    if (verificationInterval) {
        clearInterval(verificationInterval);
        verificationInterval = null;
    }

    if (verificationListener) {
        verificationListener();
        verificationListener = null;
    }
}



function setupEmailVerificationListener() {
    console.log('Setting up email verification listener');


    cleanupEmailVerificationListener();

    verificationListener = firebase.auth().onAuthStateChanged(async (user) => {
        if (!user) {
            console.log('No user found in verification listener');
            isEmailVerified = false;
            updatePromptDisplay();
            updateSendButtonState();
            return;
        }


        await checkInitialVerification(user);

        setupVerificationInterval(user);
    });
}

function setupVerificationInterval(user) {
    if (verificationInterval) {
        clearInterval(verificationInterval);
    }

    // Only set up interval if email isn't verified yet
    if (!isEmailVerified) {
        verificationInterval = setInterval(async () => {
            try {

                await user.reload();
                const newVerificationStatus = user.emailVerified;

                console.log('Checking verification status:', newVerificationStatus);


                if (newVerificationStatus !== isEmailVerified) {
                    console.log('Email verification status changed:', newVerificationStatus);
                    isEmailVerified = newVerificationStatus;

                    if (isEmailVerified) {

                        cleanupEmailVerificationListener();
                        showNotification('Email verified successfully!', 'success');

                        await checkUserCredits();
                    }

                    updatePromptDisplay();
                    updateSendButtonState();
                }
            } catch (error) {
                console.error('Error checking verification status:', error);
            }
        }, 2000);
    }
}

async function checkInitialVerification(user) {
    try {
        await user.reload();
        isEmailVerified = user.emailVerified;
        console.log('Initial verification status:', isEmailVerified);

        updatePromptDisplay();
        updateSendButtonState();

        if (isEmailVerified) {
            showNotification('Email verified successfully!', 'success');
        }
    } catch (error) {
        console.error('Error in initial verification check:', error);
    }
}


function updatePromptDisplay() {
    if (isUpdatingPrompt) {
        console.log('Prompt update already in progress, skipping...');
        return;
    }

    promptUpdateCount++;
    console.log(`updatePromptDisplay called ${promptUpdateCount} times`);

    try {
        isUpdatingPrompt = true;

        // Remove any existing prompts first
        const existingPrompt = document.querySelector('.credit-prompt');
        if (existingPrompt) {
            existingPrompt.remove();
        }

        // Get the form container - Log if not found
        const formContainer = document.querySelector('.form-container');
        if (!formContainer) {
            console.error('Form container not found');
            return;
        }

        // Create prompt div with base classes
        const promptDiv = document.createElement('div');
        promptDiv.classList.add(
            'credit-prompt',
            'mt-6',
            'mb-8',
            'p-4',
            'rounded-md',
            'shadow-sm',
            'opacity-0',
            'transition-opacity',
            'duration-500'
        );

        // Explicitly check verification status and render appropriate prompt
        console.log('Current email verification status:', isEmailVerified);

        if (!isEmailVerified) {
            console.log('User email not verified, showing subtle verification prompt');
            promptDiv.classList.add('bg-blue-50');
            promptDiv.innerHTML = `
                <div class="flex items-center justify-between">
        <p class="text-blue-800">
        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Email verification will be required when sending invitations.
        <span id="verifyEmailBtn" class="ml-2 text-blue-600 font-semibold cursor-pointer hover:underline">
           Verify now
        </span>
        </p>
            </div>
            `;

            formContainer.parentNode.insertBefore(promptDiv, formContainer);

            const verifyEmailBtn = promptDiv.querySelector('#verifyEmailBtn');
            if (verifyEmailBtn) {
                verifyEmailBtn.addEventListener('click', handleEmailVerification);
            }
        } else {
            console.log('User email verified, showing credit prompt');
            renderCreditPrompt();
        }

        // Fade in the prompt using requestAnimationFrame
        requestAnimationFrame(() => {
            promptDiv.classList.add('opacity-100');
        });

    } catch (error) {
        console.error('Error in updatePromptDisplay:', error);
    } finally {
        isUpdatingPrompt = false;
    }
}




// Global variables for retry functionality
let emailVerificationRetryCount = 0;
let emailVerificationRetryTimer = null;
const MAX_EMAIL_VERIFICATION_RETRIES = 3;
const RETRY_DELAYS = [30000, 60000, 120000]; // 30s, 1min, 2min

async function handleEmailVerification(isRetry = false) {
    try {
        const currentUser = firebase.auth().currentUser;
        if (!currentUser) {
            showNotification('No user found. Please try logging in again.', 'error');
            return;
        }

        await currentUser.sendEmailVerification();

        // Reset retry count on success
        emailVerificationRetryCount = 0;
        if (emailVerificationRetryTimer) {
            clearTimeout(emailVerificationRetryTimer);
            emailVerificationRetryTimer = null;
        }

        showNotification('Verification email sent! Please check your inbox.', 'success');
        setupEmailVerificationListener();

    } catch (error) {
        console.error('Error sending verification email:', error);
        handleEmailVerificationError(error, isRetry);
    }
}

function handleEmailVerificationError(error, isRetry = false) {
    const errorCode = error.code;

    switch (errorCode) {
        case 'auth/too-many-requests':
            handleTooManyRequestsError(isRetry);
            break;

        case 'auth/network-request-failed':
            showNotification('Network error. Please check your internet connection and try again.', 'error');
            break;

        case 'auth/user-not-found':
            showNotification('User not found. Please try logging in again.', 'error');
            break;

        case 'auth/invalid-email':
            showNotification('Invalid email address. Please contact support.', 'error');
            break;

        default:
            showNotification(`Error sending verification email: ${error.message}. Please try again later.`, 'error');
    }
}

function handleTooManyRequestsError(isRetry = false) {
    if (emailVerificationRetryCount >= MAX_EMAIL_VERIFICATION_RETRIES) {
        showNotification(
            'Too many verification attempts. Please wait 10 minutes before trying again, or contact support if the issue persists.',
            'error'
        );
        return;
    }

    const retryDelay = RETRY_DELAYS[emailVerificationRetryCount] || RETRY_DELAYS[RETRY_DELAYS.length - 1];
    const retryMinutes = Math.ceil(retryDelay / 60000);

    // Show user-friendly message with retry option
    showTooManyRequestsModal(retryDelay, retryMinutes, isRetry);
}

function showTooManyRequestsModal(retryDelay, retryMinutes, isRetry) {
    const modalHtml = `
        <div id="tooManyRequestsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Too Many Requests</h3>
                    </div>
                </div>

                <div class="mb-6">
                    <p class="text-gray-600 mb-3">
                        We've temporarily blocked verification email requests due to unusual activity. This is a security measure to protect your account.
                    </p>
                    <p class="text-gray-600">
                        You can try again in <strong id="retryCountdown">${retryMinutes} minute${retryMinutes > 1 ? 's' : ''}</strong>, or wait and try later.
                    </p>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button id="autoRetryBtn" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <span id="autoRetryText">Auto-retry in ${retryMinutes}m</span>
                    </button>
                    <button id="cancelRetryBtn" class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                </div>

                <div class="mt-4 text-sm text-gray-500">
                    <p>💡 <strong>Tip:</strong> If this keeps happening, try waiting 10-15 minutes before your next attempt.</p>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('tooManyRequestsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Setup modal functionality
    setupTooManyRequestsModal(retryDelay, retryMinutes);
}

function setupTooManyRequestsModal(retryDelay, retryMinutes) {
    const modal = document.getElementById('tooManyRequestsModal');
    const autoRetryBtn = document.getElementById('autoRetryBtn');
    const cancelRetryBtn = document.getElementById('cancelRetryBtn');
    const retryCountdown = document.getElementById('retryCountdown');
    const autoRetryText = document.getElementById('autoRetryText');

    let remainingTime = retryDelay;
    let countdownInterval;

    // Update countdown display
    function updateCountdown() {
        const minutes = Math.ceil(remainingTime / 60000);
        const seconds = Math.ceil((remainingTime % 60000) / 1000);

        if (minutes > 0) {
            retryCountdown.textContent = `${minutes} minute${minutes > 1 ? 's' : ''}`;
            autoRetryText.textContent = `Auto-retry in ${minutes}m`;
        } else {
            retryCountdown.textContent = `${seconds} second${seconds > 1 ? 's' : ''}`;
            autoRetryText.textContent = `Auto-retry in ${seconds}s`;
        }

        remainingTime -= 1000;

        if (remainingTime <= 0) {
            clearInterval(countdownInterval);
            executeRetry();
        }
    }

    // Start countdown
    countdownInterval = setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call

    // Auto-retry functionality
    function executeRetry() {
        emailVerificationRetryCount++;
        modal.remove();

        showNotification(`Retrying verification email (attempt ${emailVerificationRetryCount + 1}/${MAX_EMAIL_VERIFICATION_RETRIES + 1})...`, 'info');

        // Set timeout for the actual retry
        emailVerificationRetryTimer = setTimeout(() => {
            handleEmailVerification(true);
        }, 1000);
    }

    // Event listeners
    autoRetryBtn.addEventListener('click', () => {
        clearInterval(countdownInterval);
        executeRetry();
    });

    cancelRetryBtn.addEventListener('click', () => {
        clearInterval(countdownInterval);
        if (emailVerificationRetryTimer) {
            clearTimeout(emailVerificationRetryTimer);
            emailVerificationRetryTimer = null;
        }
        modal.remove();
        showNotification('Verification email retry cancelled. You can try again later.', 'info');
    });

    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            clearInterval(countdownInterval);
            if (emailVerificationRetryTimer) {
                clearTimeout(emailVerificationRetryTimer);
                emailVerificationRetryTimer = null;
            }
            modal.remove();
        }
    });
}



/**
 * Handles email verification requirement when user tries to send invitations
 */
async function handleEmailVerificationRequired() {
    try {
        // Check if EmailVerificationModal is available
        if (!window.EmailVerificationModal || typeof window.EmailVerificationModal.show !== 'function') {
            console.warn('EmailVerificationModal not available, using fallback confirmation dialog');

            // Fallback to simple confirmation dialog
            const userConfirmed = confirm(
                'Email verification is required to send invitations.\n\n' +
                'Would you like to send a verification email now?'
            );

            if (userConfirmed) {
                await handleEmailVerification();
                showNotification('Verification email sent! Once verified, you can return to send invitations.', 'info');
            }
            return;
        }

        // Show the email verification modal
        const userConfirmed = await window.EmailVerificationModal.show({
            title: 'Email Verification Required',
            message: 'We\'ve noticed you\'re trying to send an invitation. Please verify your email address first to continue.',
            confirmText: 'Send Verification Email',
            cancelText: 'Cancel'
        });

        if (userConfirmed) {
            // User clicked "Send Verification Email"
            await handleEmailVerification();

            // Show additional guidance
            showNotification('Verification email sent! Once verified, you can return to send invitations.', 'info');
        }

    } catch (error) {
        console.error('Error handling email verification requirement:', error);
        showNotification('Error showing verification modal. Please try again.', 'error');
    }
}





function setupCreditListener() {
    try {
        const currentUser = firebase.auth().currentUser;
        if (!currentUser || !currentUser.email) {
            console.error('No authenticated user found');
            return;
        }

        if (creditListener) {
            creditListener();
        }

        const adminRef = db.collection('Admins').doc(currentUser.email);
        creditListener = adminRef.onSnapshot((doc) => {
            if (doc.exists) {
                const adminData = doc.data();
                const oldCredits = userCredits;
                const oldPaidStatus = isPaidUser;

                isPaidUser = adminData.paid || false;
                userCredits = adminData.credits ?? 0;

                // Only update if there's an actual change in credits or paid status
                if (oldCredits !== userCredits || oldPaidStatus !== isPaidUser) {
                    console.log('Credit status changed, updating display');
                    if (isEmailVerified) {
                        renderCreditPrompt();
                    } else {
                        updatePromptDisplay();
                    }
                    updateSendButtonState();
                    updateInviteeSummaryUI(); // Update to show new credits value
                }
            }
        }, (error) => {
            console.error('Error listening to credit changes:', error);
        });

    } catch (error) {
        console.error('Error setting up credit listener:', error);
    }
}


function cleanupCreditListener() {
    if (creditListener) {
        creditListener = null;
    }
}

// Cleanup function for email verification retry functionality
function cleanupEmailVerificationRetry() {
    if (emailVerificationRetryTimer) {
        clearTimeout(emailVerificationRetryTimer);
        emailVerificationRetryTimer = null;
    }

    // Remove any existing modal
    const existingModal = document.getElementById('tooManyRequestsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Reset retry count
    emailVerificationRetryCount = 0;
}

// Add cleanup on page unload
window.addEventListener('beforeunload', () => {
    cleanupEmailVerificationRetry();
});



  // Select Credit Package and Initialize PayPal
  function selectCreditPackage(pkg) {
    selectedPackage = pkg;

    // Highlight selected button
    document.querySelectorAll(".select-package-btn").forEach((btn) => {
      btn.textContent = "Select";
      btn.classList.remove("bg-blue-500", "text-white");
      btn.classList.add("bg-light");
    });
    const button = document.querySelector(`.credit-package[data-credits="${pkg.credits}"] .select-package-btn`);
    button.textContent = "Selected";
    button.classList.add("bg-blue-500", "text-white");

    // Show PayPal container
    const paypalContainer = document.getElementById("paypal-button-container");
    paypalContainer.classList.remove("hidden");
    initializePayPalForCredits();
  }

  async function checkUserCredits() {
    try {
        const currentUser = firebase.auth().currentUser;
        if (!currentUser || !currentUser.email) {
            console.error('No authenticated user found');
            return;
        }

        const adminDoc = await db.collection('Admins').doc(currentUser.email).get();
        if (adminDoc.exists) {
            const adminData = adminDoc.data();
            isPaidUser = adminData.paid || false;
            userCredits = adminData.credits ?? 0;

            renderCreditPrompt();
            updateSendButtonState();
            updateInviteeSummaryUI(); // Update to show credits left
        }

        setupCreditListener();

    } catch (error) {
        console.error('Error checking user credits:', error);
    }
}


async function updateUserCredits(newCreditCount) {
    try {
        const currentUser = firebase.auth().currentUser;
        if (!currentUser) {
            throw new Error('No authenticated user found');
        }

        await db.collection('Admins').doc(currentUser.email).update({
            credits: newCreditCount
        });

        userCredits = newCreditCount;
        renderCreditPrompt();
        updateInviteeSummaryUI(); // Update to show new credits
    } catch (error) {
        console.error('Error updating user credits:', error);
        throw error;
    }
}

function renderCreditPrompt() {
    creditPromptCount++;
    console.log(`renderCreditPrompt called ${creditPromptCount} times`);
    if (!isEmailVerified) {
        return;
    }

    const existingPrompt = document.querySelector('.credit-prompt');
    if (existingPrompt) {
        existingPrompt.remove();
    }

    if (!isPaidUser || (isPaidUser && userCredits <= 5)) {
        const formContainer = document.querySelector('.form-container');
        if (!formContainer) return;

        const promptDiv = document.createElement('div');
        promptDiv.classList.add(
            'credit-prompt',
            'mt-6',
            'mb-8',
            'p-4',
            'rounded-md',
            'shadow-sm',
            'opacity-0',
            'transition-opacity',
            'duration-500'
        );

        if (!isPaidUser) {
            promptDiv.classList.add('bg-blue-50');
            promptDiv.innerHTML = `
            <div class="flex justify-between items-start">
                <p style="color: #121c41;" class="flex-grow">
                    Good news! We've awarded you 5 free credits to start assessing your team. You have ${userCredits} credits remaining. Need more?
                    <span id="invitePageAddCredits" style="color: #1547bb; font-weight: 600;" class="ml-2 cursor-pointer hover:underline">
                    Add credits
                    </span>
                </p>
                <button class="dismiss-prompt ml-4 text-gray-500 hover:text-gray-700 transition-colors duration-150 ease-in-out">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            `;
        } else if (userCredits <= 5) {
            promptDiv.classList.add('bg-yellow-50');
            promptDiv.innerHTML = `
                <p style="color: #121c41;">
                    Your credit balance is running low (${userCredits} credits remaining).
                    <span id="invitePageAddCredits" style="color: #1547bb; text-shadow: none;" class="ml-2 font-semibold cursor-pointer hover:underline">
                        Add more credits
                    </span>
                </p>
            `;
        }

        formContainer.parentNode.insertBefore(promptDiv, formContainer);

        // Fade in the prompt
        requestAnimationFrame(() => {
            promptDiv.classList.add('opacity-100');
        });

        const addCreditsBtn = promptDiv.querySelector('#invitePageAddCredits');
        if (addCreditsBtn) {
            addCreditsBtn.addEventListener('click', async () => {
                // Check user's subscription type
                const user = firebase.auth().currentUser;
                if (!user) {
                    showNotification('You must be logged in to add credits.', 'error');
                    return;
                }

                try {
                    const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
                    if (!doc.exists) {
                        showNotification('User not found.', 'error');
                        return;
                    }

                    const userData = doc.data();
                    const isFreeTrial = userData.subscriptionType === 'freeTrial';

                    // Show appropriate modal based on subscription type
                    if (isFreeTrial) {
                        // For free trial users, show subscription modal
                        if (window.SubscriptionModal) {
                            window.SubscriptionModal.show();
                        } else {
                            console.error('SubscriptionModal not found');
                            showNotification('Unable to load subscription options. Please try again later.', 'error');
                        }
                    } else {
                        // For paid users, show top-up modal
                        if (window.TopupModal) {
                            window.TopupModal.show();
                        } else {
                            console.error('TopupModal not found');
                            showNotification('Unable to load top-up options. Please try again later.', 'error');
                        }
                    }
                } catch (error) {
                    console.error('Error checking subscription type:', error);
                    showNotification('An error occurred. Please try again later.', 'error');
                }
            });
        }
        const dismissBtn = promptDiv.querySelector('.dismiss-prompt');
        if (dismissBtn) {
            dismissBtn.addEventListener('click', () => {
                promptDiv.remove();
            });
        }
    }
}


function updateSendButtonState() {
    const sendInvitationsButton = document.getElementById('sendInvitationsButton');
    if (!sendInvitationsButton) return;

    const invitees = JSON.parse(localStorage.getItem('invitees')) || [];

    // Email verification is now handled in sendInvitations() function
    // Button remains enabled but will show modal if email not verified

    // Check credits
    if (userCredits === 0 || (!isPaidUser && invitees.length > userCredits)) {
        sendInvitationsButton.disabled = true;
        sendInvitationsButton.classList.add('opacity-50', 'cursor-not-allowed');

        if (userCredits === 0) {
            showNotification('You need credits to send invitations. Please purchase credits to continue.', 'error');
        } else {
            showNotification('Not enough credits to send invitations. Please purchase more credits.', 'error');
        }
    } else {
        sendInvitationsButton.disabled = false;
        sendInvitationsButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }
}





async function initializeInviteUsers(userCompany) {
    console.log('Initializing invite users...');

    try {
        firebase.auth().onAuthStateChanged(async (user) => {
            console.log('Auth state changed. User:', user ? 'logged in' : 'not logged in');

            if (user) {
                console.log('User found, checking email verification...');
                await checkEmailVerification();
                updatePromptDisplay();
            } else {
                console.log('No user found, clearing verification interval');
                if (verificationInterval) {
                    clearInterval(verificationInterval);
                    verificationInterval = null;
                }
                isEmailVerified = false;
                updatePromptDisplay();
            }
        });

        await Promise.all([
            checkUserCredits(),
            fetchInvitationSummary()
        ]);

        initializeUIComponents();
        setupInviteEventListeners(); // Changed from setupEventListeners
        renderInvitees();
        setupCreditListener();

    } catch (error) {
        console.error('Error initializing invite users:', error);
        updatePromptDisplay();
    }
}


function initializeUIComponents() {
    const manualTab = document.getElementById('manualTab');
    const csvTab = document.getElementById('csvTab');
    const manualSection = document.getElementById('manualSection');
    const csvSection = document.getElementById('csvSection');

    if (manualTab && csvTab) {
        manualTab.addEventListener('click', () => {
            switchTab(manualTab, manualSection, csvTab, csvSection);
        });

        csvTab.addEventListener('click', () => {
            switchTab(csvTab, csvSection, manualTab, manualSection);
        });
    }
}


async function initializePaymentFeatures() {
    if (window.PaymentHandler) {

        await window.PaymentHandler.initializePaymentUI();


        const addCreditsBtn = document.getElementById('invitePageAddCredits');
        if (addCreditsBtn) {
            addCreditsBtn.addEventListener('click', () => {
                window.PaymentHandler.showCreditPurchaseModal();
            });
        }
    }
}

// Add this new function to handle assessment type changes
function switchAssessmentType(type) {
    assessmentType = type;

    // Update the web link based on selected type
    if (type === 'digital') {
        companyWebLink = 'https://assessment.skillsassess.ai/SGA.html';
    } else {
        companyWebLink = 'https://skillsassess-softskills.onrender.com/SGA.html';
    }

    // Update UI to show active selection
    const digitalSelector = document.getElementById('digitalSkillsSelector');
    const softSkillsSelector = document.getElementById('softSkillsSelector');

    if (digitalSelector && softSkillsSelector) {
        if (type === 'digital') {
            digitalSelector.classList.add('active-assessment');
            softSkillsSelector.classList.remove('active-assessment');
        } else {
            softSkillsSelector.classList.add('active-assessment');
            digitalSelector.classList.remove('active-assessment');
        }
    }
}

function setupInviteEventListeners() {
    console.log('Setting up invite event listeners...');

    // Send invitations button
    const sendInvitationsButton = document.getElementById('sendInvitationsButton');
    if (sendInvitationsButton) {
        const newButton = sendInvitationsButton.cloneNode(true);
        sendInvitationsButton.parentNode.replaceChild(newButton, sendInvitationsButton);
        newButton.addEventListener('click', async (e) => {
            e.preventDefault();
            await sendInvitations();
        });
    }

    // Initialize manual entry
    initializeManualEntry();

    // CSV upload
    const uploadButton = document.getElementById('uploadButton');
    if (uploadButton) {
        const newButton = uploadButton.cloneNode(true);
        uploadButton.parentNode.replaceChild(newButton, uploadButton);
        newButton.style.backgroundColor = "#1547bb"; // Set upload button color
        newButton.addEventListener('click', handleCSVUpload);
    }

    // View details
    const viewDetailsBtn = document.getElementById('viewDetailsBtn');
    if (viewDetailsBtn) {
        const newButton = viewDetailsBtn.cloneNode(true);
        viewDetailsBtn.parentNode.replaceChild(newButton, viewDetailsBtn);
        newButton.addEventListener('click', toggleDetailsView);
    }

    // Remove invitee listeners
    addRemoveInviteeListeners();
    addTestAssessmentButton();

    // Add assessment type selector listeners
    const digitalSelector = document.getElementById('digitalSkillsSelector');
    const softSkillsSelector = document.getElementById('softSkillsSelector');

    if (digitalSelector) {
        digitalSelector.addEventListener('click', () => switchAssessmentType('digital'));
    }

    if (softSkillsSelector) {
        softSkillsSelector.addEventListener('click', () => switchAssessmentType('soft'));
    }
}

function switchTab(activeTab, activeSection, inactiveTab, inactiveSection) {
    activeTab.classList.add('active-tab');
    inactiveTab.classList.remove('active-tab');
    inactiveSection.classList.remove('active');
    setTimeout(() => {
        activeSection.classList.add('active');
    }, 50);
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Make sure to update renderInvitees to call updateSendButtonState
function renderInvitees() {
    const inviteesList = document.getElementById('inviteesContainer');
    if (!inviteesList) return;

    const invitees = JSON.parse(localStorage.getItem('invitees')) || [];

    inviteesList.innerHTML = '';
    invitees.forEach((invitee, index) => {
        const listItem = document.createElement('div');
        listItem.className = 'invitee-item';
        listItem.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <p class="font-medium">${sanitizeInput(invitee.firstName)} ${sanitizeInput(invitee.lastName)}</p>
                    <p class="text-gray-600">${sanitizeInput(invitee.email)}</p>
                </div>
                <button class="remove-invitee text-red-500 text-xl hover:text-red-700 ml-2" data-index="${index}">&times;</button>
            </div>
        `;
        inviteesList.appendChild(listItem);
    });
    updateInviteeSummaryUI();
    updateSendButtonState();
}

async function fetchInvitationSummary() {
    try {
        const summaryRef = db.collection('companies').doc(userCompany).collection('inviteeSummary').doc('summary');
        const doc = await summaryRef.get();
        if (doc.exists) {
            invitationSummary = doc.data();
            sentEmails = invitationSummary.sentEmails || [];
            failedEmails = invitationSummary.failedEmails || [];
            console.log('Fetched sentEmails:', sentEmails);
            console.log('Fetched failedEmails:', failedEmails);
        } else {
            invitationSummary = { sent: 0, failed: 0 };
            sentEmails = [];
            failedEmails = [];
        }
        updateInviteeSummaryUI();
    } catch (error) {
        console.error('Error fetching invitation summary:', error);
        invitationSummary = { sent: 0, failed: 0 };
        sentEmails = [];
        failedEmails = [];
        updateInviteeSummaryUI();
    }
}


function updateInviteeSummaryUI() {
    const emailsSent = document.getElementById('emailsSent');
    const emailsFailed = document.getElementById('emailsFailed');
    const creditsLeft = document.getElementById('creditsLeft');

    if (emailsSent) emailsSent.textContent = invitationSummary.sent;
    if (emailsFailed) emailsFailed.textContent = invitationSummary.failed;
    if (creditsLeft) creditsLeft.textContent = userCredits;

    updateDetailedLists();
}

function toggleDetailsView() {
    const detailsContainer = document.getElementById('detailsContainer');
    const viewDetailsBtn = document.getElementById('viewDetailsBtn');

    detailsVisible = !detailsVisible;

    if (detailsVisible) {
        detailsContainer.classList.remove('hidden');
        detailsContainer.style.maxHeight = detailsContainer.scrollHeight + 'px';
        viewDetailsBtn.textContent = 'Hide Details';
    } else {
        detailsContainer.style.maxHeight = '0px';
        viewDetailsBtn.textContent = 'View Details';
        setTimeout(() => {
            detailsContainer.classList.add('hidden');
        }, 300);
    }
}


function updateDetailedLists() {
    const sentEmailsList = document.getElementById('sentEmailsList');
    const failedEmailsList = document.getElementById('failedEmailsList');

    if (sentEmailsList) {
        if (sentEmails.length > 0) {
            sentEmailsList.innerHTML = sentEmails.map(email => `<li>${email}</li>`).join('');
        } else {
            sentEmailsList.innerHTML = '<li>No emails have been sent yet.</li>';
        }
    }

    if (failedEmailsList) {
        if (failedEmails.length > 0) {
            failedEmailsList.innerHTML = failedEmails.map(email => `<li>${email}</li>`).join('');
        } else {
            failedEmailsList.innerHTML = '<li>No failed emails.</li>';
        }
    }
}

// Function to render the list of invitees
function renderInvitees() {
    const inviteesList = document.getElementById('inviteesContainer');
    if (!inviteesList) return;

    const invitees = JSON.parse(localStorage.getItem('invitees')) || [];

    inviteesList.innerHTML = '';
    invitees.forEach((invitee, index) => {
        const listItem = document.createElement('div');
        listItem.className = 'invitee-item';
        listItem.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <p class="font-medium">${sanitizeInput(invitee.firstName)} ${sanitizeInput(invitee.lastName)}</p>
                    <p class="text-gray-600">${sanitizeInput(invitee.email)}</p>
                </div>
                <button class="remove-invitee text-red-500 text-xl hover:text-red-700 ml-2" data-index="${index}">&times;</button>
            </div>
        `;
        inviteesList.appendChild(listItem);
    });
    updateInviteeSummaryUI();
    updateSendButtonState();
}

function initializeManualEntry() {
    // Clear any existing event listeners by removing and recreating the button
    const addButton = document.getElementById('addButton');
    if (!addButton) return;

    const newAddButton = addButton.cloneNode(true);
    addButton.parentNode.replaceChild(newAddButton, addButton);

    // Add the single click handler
    newAddButton.addEventListener('click', handleManualAddition);

    // Set up enter key handlers
    const inputs = ['firstNameInput', 'lastNameInput', 'emailInput'].map(id =>
        document.getElementById(id)
    ).filter(Boolean);

    inputs.forEach(input => {
        // Remove existing listeners
        const newInput = input.cloneNode(true);
        input.parentNode.replaceChild(newInput, input);

        // Add new enter key handler
        newInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleManualAddition(e);
            }
        });
    });
}


function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function handleAddInvitee(e) {
    e.preventDefault();

    const firstNameInput = document.getElementById('firstNameInput');
    const lastNameInput = document.getElementById('lastNameInput');
    const emailInput = document.getElementById('emailInput');

    const firstName = firstNameInput?.value?.trim() || '';
    const lastName = lastNameInput?.value?.trim() || '';
    const email = emailInput?.value?.trim() || '';

    console.log('Input values:', { firstName, lastName, email });

    if (!firstName || !lastName || !email) {
        console.log('Validation failed - missing fields');
        showNotification('Please enter First Name, Last Name, and Email.', 'error');
        return;
    }

    if (!validateEmail(email)) {
        console.log('Validation failed - invalid email format');
        showNotification('Please enter a valid email address.', 'error');
        return;
    }

    try {
        let invitees = JSON.parse(localStorage.getItem('invitees')) || [];
        invitees.push({ firstName, lastName, email });
        localStorage.setItem('invitees', JSON.stringify(invitees));
        renderInvitees();

        firstNameInput.value = '';
        lastNameInput.value = '';
        emailInput.value = '';

        showNotification('Invitee added successfully!', 'success');
    } catch (error) {
        console.error('Error adding invitee:', error);
        showNotification('Error adding invitee. Please try again.', 'error');
    }
}

// Function to handle manual entry of invitees
async function handleManualAddition(e) {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
        console.log('Submission already in progress');
        return;
    }

    try {
        isSubmitting = true;

        // Get input elements
        const firstNameInput = document.getElementById('firstNameInput');
        const lastNameInput = document.getElementById('lastNameInput');
        const emailInput = document.getElementById('emailInput');

        // Validate elements exist
        if (!firstNameInput || !lastNameInput || !emailInput) {
            throw new Error('Form inputs not found');
        }

        // Get and validate values
        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();
        const email = emailInput.value.trim();

        // Check for empty fields
        if (!firstName || !lastName || !email) {
            showNotification('Please enter First Name, Last Name, and Email.', 'error');
            return;
        }

        // Validate email format
        if (!validateEmail(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Get current invitees
        let invitees = [];
        try {
            invitees = JSON.parse(localStorage.getItem('invitees')) || [];
        } catch (error) {
            console.error('Error parsing invitees from localStorage:', error);
            invitees = [];
        }

        // Check for duplicates
        const isDuplicate = invitees.some(invitee =>
            invitee.email.toLowerCase() === email.toLowerCase()
        );

        if (isDuplicate) {
            showNotification('This email address has already been added.', 'warning');
            return;
        }

        // Add new invitee
        invitees.push({ firstName, lastName, email });
        localStorage.setItem('invitees', JSON.stringify(invitees));

        // Clear form
        firstNameInput.value = '';
        lastNameInput.value = '';
        emailInput.value = '';

        // Update UI
        renderInvitees();
        showNotification('Invitee added successfully!', 'success');

    } catch (error) {
        console.error('Error in handleManualAddition:', error);
        showNotification('Error adding invitee. Please try again.', 'error');
    } finally {
        // Reset submission flag after a short delay
        setTimeout(() => {
            isSubmitting = false;
        }, 500);
    }
}

// Helper function to validate email format
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function handleCSVUpload() {
    const csvFileInput = document.getElementById('csvFileInput');
    const csvInstruction = document.getElementById('csvInstruction');

    // Directly run CSV processing on click, rather than adding another click listener
    if (csvFileInput.files.length > 0) {
        const file = csvFileInput.files[0];
        const reader = new FileReader();
        reader.onload = function(e) {
            const csv = e.target.result;
            processCSV(csv);
        };
        reader.readAsText(file);
    } else {
        showNotification('Please select a CSV file first.', 'error');
    }

    function processCSV(csv) {
        Papa.parse(csv, {
            header: true,
            skipEmptyLines: true,
            complete: function(results) {
                const data = results.data;
                const headers = results.meta.fields;

                // Validate CSV structure
                if (!headers.includes('First Name') || !headers.includes('Last Name') || !headers.includes('Email')) {
                    csvInstruction.classList.add('shake');
                    csvInstruction.style.color = 'red';
                    setTimeout(() => {
                        csvInstruction.classList.remove('shake');
                    }, 500);
                    showNotification('CSV file must contain "First Name", "Last Name", and "Email" columns.', 'error');
                    return;
                }

                // Reset text color if previously set to red
                csvInstruction.style.color = '';

                // Get current invitees
                let currentInvitees = JSON.parse(localStorage.getItem('invitees')) || [];

                // Filter valid entries from CSV
                const validEntries = data.filter(row => {
                    const firstName = row['First Name'] ? row['First Name'].trim() : '';
                    const lastName = row['Last Name'] ? row['Last Name'].trim() : '';
                    const email = row['Email'] ? row['Email'].trim() : '';
                    return firstName && lastName && email && validateEmail(email);
                });

                // Check total count against credits for free users
                const totalCount = currentInvitees.length + validEntries.length;

                if (!isPaidUser && totalCount > userCredits) {
                    // Calculate how many entries we can actually add
                    const availableSlots = Math.max(0, userCredits - currentInvitees.length);

                    if (availableSlots === 0) {
                        showNotification(`Cannot add more entries. You have reached your credit limit of ${userCredits}. Please upgrade to premium for unlimited entries.`, 'error');
                        return;
                    }

                    // Take only the entries that fit within the credit limit
                    const limitedEntries = validEntries.slice(0, availableSlots);

                    // Add the limited entries
                    limitedEntries.forEach(row => {
                        currentInvitees.push({
                            firstName: row['First Name'].trim(),
                            lastName: row['Last Name'].trim(),
                            email: row['Email'].trim()
                        });
                    });

                    // Show notification about partial upload
                    showNotification(
                        `Added ${limitedEntries.length} out of ${validEntries.length} entries. ` +
                        `Credit limit reached. Upgrade to premium to add all ${validEntries.length} entries.`,
                        'warning'
                    );
                } else {
                    // Add all valid entries for paid users or users within credit limit
                    validEntries.forEach(row => {
                        currentInvitees.push({
                            firstName: row['First Name'].trim(),
                            lastName: row['Last Name'].trim(),
                            email: row['Email'].trim()
                        });
                    });

                    // Show success notification
                    showNotification(`Successfully added ${validEntries.length} entries.`, 'success');
                }

                // Update localStorage and UI
                localStorage.setItem('invitees', JSON.stringify(currentInvitees));
                renderInvitees();

                // Clear file input
                csvFileInput.value = '';
            },
            error: function(err) {
                console.error('Error parsing CSV:', err);
                showNotification('An error occurred while parsing the CSV file.', 'error');
            }
        });
    }
}

// Function to add listeners for removing invitees
function addRemoveInviteeListeners() {
    const inviteesContainer = document.getElementById('inviteesContainer');
    inviteesContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-invitee')) {
            const index = parseInt(e.target.getAttribute('data-index'), 10);
            let invitees = JSON.parse(localStorage.getItem('invitees')) || [];
            invitees.splice(index, 1);
            localStorage.setItem('invitees', JSON.stringify(invitees));
            renderInvitees();
        }
    });
}

async function sendInvitations() {
    const sendInvitationsButton = document.getElementById('sendInvitationsButton');
    if (!sendInvitationsButton) {
        console.error('Send invitations button not found');
        return;
    }

    // Check email verification first
    if (!isEmailVerified) {
        await handleEmailVerificationRequired();
        return;
    }

    const originalState = {
        text: sendInvitationsButton.textContent,
        disabled: sendInvitationsButton.disabled
    };

    try {
        sendInvitationsButton.disabled = true;
        sendInvitationsButton.textContent = 'Sending...';

        const invitees = JSON.parse(localStorage.getItem('invitees')) || [];
        if (invitees.length === 0) {
            throw new Error('No invitees to send invitations to.');
        }

        if (!isPaidUser && invitees.length > userCredits) {
            throw new Error('Not enough credits to send invitations. Please upgrade to premium.');
        }

        // Check if API_URL is defined
        if (typeof API_URL === 'undefined') {
            throw new Error('API URL is not defined. Please check your configuration.');
        }

        const response = await fetch(`${API_URL}/send-invitations`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ invitees, userCompany, webLink: companyWebLink }),
        });

        if (!response.ok) {
            throw new Error(`Failed to send invitations: ${response.statusText}`);
        }

        const results = await response.json();

        // Update credits for free users
        if (!isPaidUser) {
            const newCreditCount = userCredits - results.sent.length;
            await updateUserCredits(newCreditCount);
        }

        // Update invitees list
        const updatedInvitees = invitees.filter(invitee => !results.sent.includes(invitee.email));
        localStorage.setItem('invitees', JSON.stringify(updatedInvitees));

        // Update Firebase
        await updateFirebaseSummary(results.sent.length, results.failed.length);
        await storeEmailsInFirebase(results.sent, results.failed);

        renderInvitees();

        showNotification(`Successfully sent ${results.sent.length} invitations. Failed: ${results.failed.length}`, 'success');

    } catch (error) {
        console.error('Error in sendInvitations:', error);
        showNotification(error.message || 'An error occurred while sending invitations.', 'error');
    } finally {
        requestAnimationFrame(() => {
            const button = document.getElementById('sendInvitationsButton');
            if (button) {
                button.textContent = originalState.text;
                button.disabled = originalState.disabled;
                updateSendButtonState();
            }
        });
    }
}



async function updateFirebaseSummary(sentCount, failedCount) {
    try {
        const summaryRef = db.collection('companies').doc(userCompany).collection('inviteeSummary').doc('summary');
        await summaryRef.set({
            sent: firebase.firestore.FieldValue.increment(sentCount),
            failed: firebase.firestore.FieldValue.increment(failedCount)
        }, { merge: true });

        invitationSummary.sent += sentCount;
        invitationSummary.failed += failedCount;
        updateInviteeSummaryUI();
    } catch (error) {
        console.error('Error updating Firebase summary:', error);
    }
}


async function storeEmailsInFirebase(sentEmailsArray, failedEmailsArray) {
    try {
        const summaryRef = db.collection('companies').doc(userCompany).collection('inviteeSummary').doc('summary');
        await summaryRef.set({
            sentEmails: firebase.firestore.FieldValue.arrayUnion(...sentEmailsArray),
            failedEmails: firebase.firestore.FieldValue.arrayUnion(...failedEmailsArray)
        }, { merge: true });

        console.log('Emails stored in Firestore');

        sentEmails.push(...sentEmailsArray);
        failedEmails.push(...failedEmailsArray);

        updateInviteeSummaryUI();
    } catch (error) {
        console.error('Error storing emails in Firestore:', error);
    }
}

function validateEmail(email) {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
}

function sanitizeInput(input) {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
}

function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    const notificationContainer = document.createElement('div');
    notificationContainer.classList.add('notification');

    Object.assign(notificationContainer.style, {
        position: 'fixed',
        top: '70px',
        left: '50%',
        transform: 'translateX(-50%)',
        padding: '12px 24px',
        borderRadius: '6px',
        opacity: '0',
        transition: 'opacity 0.3s ease',
        zIndex: '9999',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        fontWeight: '500'
    });

    // Add type-specific styles
    if (type === 'success') {
        notificationContainer.classList.add('success');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#48bb78',
            color: 'white'
        });
    } else if (type === 'error') {
        notificationContainer.classList.add('error');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEE2E2',
            color: '#991B1B',
            border: '1px solid #FCA5A5'
        });
    } else if (type === 'warning') {
        notificationContainer.classList.add('warning');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#FEF3C7',
            color: '#92400E',
            border: '1px solid #FCD34D'
        });
    } else if (type === 'info') {
        notificationContainer.classList.add('info');
        Object.assign(notificationContainer.style, {
            backgroundColor: '#DBEAFE',
            color: '#1E40AF',
            border: '1px solid #93C5FD'
        });
    }

    notificationContainer.textContent = message;


    document.body.appendChild(notificationContainer);

    // Trigger fade in
    requestAnimationFrame(() => {
        notificationContainer.style.opacity = '1';
    });

    // Remove after delay
    setTimeout(() => {
        notificationContainer.style.opacity = '0';
        setTimeout(() => {
            notificationContainer.remove();
        }, 300); // Wait for fade out animation to complete
    }, 4000);
}

// New function: Handles test-assessment click and adds admin email if missing
async function handleTestAssessmentClick() {
    const currentUser = firebase.auth().currentUser;
    if (!currentUser || !currentUser.email) {
        showNotification('No authenticated admin found.', 'error');
        return;
    }

    try {
        showLoadingOverlay();
        const adminEmail = currentUser.email.toLowerCase();

        // Check Firebase for existing invitee
        const summaryRef = db.collection('companies').doc(userCompany).collection('inviteeSummary').doc('summary');
        const summaryDoc = await summaryRef.get();

        let existsInFirebase = false;
        if (summaryDoc.exists) {
            const sentEmails = summaryDoc.data().sentEmails || [];
            existsInFirebase = sentEmails.includes(adminEmail);
        }

        if (existsInFirebase) {
            // If email exists in Firebase, proceed directly to showing modal
            showTestAssessmentModal(adminEmail);
            return;
        }

        // If not in Firebase, add it to the invitee summary
        try {
            await summaryRef.set({
                sent: firebase.firestore.FieldValue.increment(1),
                sentEmails: firebase.firestore.FieldValue.arrayUnion(adminEmail)
            }, { merge: true });

            console.log('Added admin email to invitee summary:', adminEmail);
            showNotification('Successfully added to assessment list', 'success');

            // Show the test assessment modal
            showTestAssessmentModal(adminEmail);
        } catch (dbError) {
            console.error('Error updating Firebase:', dbError);
            showNotification('Error adding to assessment list', 'error');
            throw dbError;
        }

    } catch (error) {
        console.error('Error in handleTestAssessmentClick:', error);
        showNotification('Error processing test assessment request.', 'error');
    } finally {
        hideLoadingOverlay();
    }
}



function showTestAssessmentModal(adminEmail) {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.id = 'testAssessmentModal';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50';

    // Modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white p-6 rounded-lg max-w-md w-full text-center relative';

    // Close (X) button in top-right
    const closeButton = document.createElement('button');
    closeButton.className = 'absolute top-3 right-3 text-gray-500 hover:text-gray-700 transition-colors';
    closeButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
             viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M6 18L18 6M6 6l12 12" />
        </svg>
    `;
    closeButton.addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    // Message
    const message = document.createElement('p');
    message.className = 'text-gray-700 mb-4';
    message.innerHTML = `
        You will be redirected to the <strong>${assessmentType === 'digital' ? 'Digital Skills' : 'Soft Skills'}</strong> assessment.
        <br/>
        Please use <strong>${adminEmail}</strong> when filling the form.
        <br/>
        <small>(Click the copy icon to copy your email)</small>
    `;

    // Copy button (with icon)
    const copyButton = document.createElement('button');
    copyButton.className = 'flex items-center justify-center space-x-2 px-4 py-2 text-white rounded transition-colors';
    copyButton.style.backgroundColor = '#121c41';
    copyButton.style.hover = '#1a2959';  // slightly lighter shade for hover
    copyButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none"
             viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round"
                d="M9 2L5 6v14a2 2 0 002 2h10a2 2 0 002-2V6l-4-4H9z" />
        </svg>
        <span>Copy</span>
    `;

    copyButton.addEventListener('click', () => {
        navigator.clipboard.writeText(adminEmail);
        showNotification('Email copied!', 'success');
    });

    // "Take test" button
    const takeTestButton = document.createElement('button');
    takeTestButton.textContent = 'Take test';
    takeTestButton.className = 'px-4 py-2 text-white rounded transition-colors';
    takeTestButton.style.backgroundColor = '#1547bb';
    takeTestButton.style.hover = '#1a51d4';  // slightly lighter shade for hover
    takeTestButton.addEventListener('click', () => {
        window.open(companyWebLink, '_blank');
        document.body.removeChild(overlay);
    });

    // Container for copy & "Take test" buttons
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'mt-6 flex items-center justify-center space-x-4';

    // Append buttons to the container
    buttonContainer.appendChild(copyButton);
    buttonContainer.appendChild(takeTestButton);

    // Assemble modal elements
    modalContainer.appendChild(closeButton);    // X button in top-right
    modalContainer.appendChild(message);
    modalContainer.appendChild(buttonContainer);

    overlay.appendChild(modalContainer);
    document.body.appendChild(overlay);
}

function addTestAssessmentButton() {
  // Append below the form-container if it exists
  const formContainer = document.querySelector('.form-container');
  if (!formContainer) return;

  // Create button if not already added
  if (!document.getElementById('testAssessmentBtn')) {
    const sendInvitationsButton = document.getElementById('sendInvitationsButton');
    if (!sendInvitationsButton) return;

    const testBtn = document.createElement('button');
    testBtn.id = 'testAssessmentBtn';

    // Insert Heroicon (Clipboard Document Check) SVG + text
    // NOTE: You can replace the path with any other Heroicons SVG path as needed.
    testBtn.innerHTML = `
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="h-5 w-5 mr-2"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M10.125 9h3.75m-3.75 3.75h3.75m-7.5 3.75h11.25m-.75
             4.5H6.75a2 2 0 01-2 2.25V5.625c0-1.24
             1.01-2.25 2.25-2.25h2.106c.596 0 1.169.237
             1.591.66l.53.53c.422.422.995.66
             1.59.66h2.106a2.25 2.25 0
             012.25 2.25v13.5a2.25 2.25 0
             01-2.25 2.25z"
        />
      </svg>
      Try the assessment yourself
    `;

    // Use Tailwind-like classes to style the button (adjust as needed)
    testBtn.className = `
      flex
      items-center
      mt-4
      px-5
      py-3
      bg-green-500
      text-white
      font-semibold
      rounded-md
      shadow
      hover:bg-green-600
      focus:outline-none
      transition
      duration-200
    `;

    sendInvitationsButton.parentNode.insertBefore(testBtn, sendInvitationsButton.nextSibling);
    testBtn.addEventListener('click', handleTestAssessmentClick);
  }
}


window.sendInvitations = sendInvitations;
