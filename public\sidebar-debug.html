<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Debug Dashboard</title>
    <link rel="stylesheet" href="sidebar-styles.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-height: 80vh;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .debug-section {
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .debug-section h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        .debug-item {
            margin: 4px 0;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        .test-controls {
            margin: 10px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
            font-size: 11px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h3>Sidebar Debug Dashboard</h3>
        
        <div class="test-controls">
            <button class="test-btn" onclick="runAllTests()">Run All Tests</button>
            <button class="test-btn" onclick="testSidebarElements()">Test Elements</button>
            <button class="test-btn" onclick="testDemoMode()">Test Demo Mode</button>
            <button class="test-btn" onclick="testNavigation()">Test Navigation</button>
            <button class="test-btn" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="debug-results"></div>
    </div>

    <!-- Include the actual sidebar from main.html -->
    <!-- Mobile Toggle Button (shown when sidebar is collapsed on mobile) -->
    <button id="mobile-sidebar-toggle" class="mobile-sidebar-toggle" title="Open Menu">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Sidebar Overlay (Mobile) -->
    <div id="sidebar-overlay" class="sidebar-overlay hidden"></div>

    <!-- Compact Left Sidebar Navigation -->
    <aside id="sidebar" class="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="logo.png" alt="Barefoot elearning" id="logo-image" class="logo-img">
                <span class="logo-text">SkillsAssess</span>
            </div>
            <button id="sidebar-toggle" class="sidebar-toggle" title="Toggle Sidebar">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>

        <!-- Main Navigation -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <h3 class="nav-section-title">Main</h3>
                <a href="#" class="nav-item" data-content="dashboard">
                    <img src="home.png" alt="Dashboard" class="nav-icon">
                    <span class="nav-text">Dashboard</span>
                </a>
                <a href="#" class="nav-item" data-content="assessments">
                    <img src="audit.png" alt="Assessments" class="nav-icon">
                    <span class="nav-text">Assessments</span>
                </a>
                <a href="#" class="nav-item" data-content="learning-paths">
                    <img src="online-learning.png" alt="Learning Paths" class="nav-icon">
                    <span class="nav-text">Learning Paths</span>
                </a>
                <a href="#" class="nav-item" data-content="reports">
                    <img src="reports.png" alt="Metrics" class="nav-icon">
                    <span class="nav-text">Metrics</span>
                </a>
                <a href="#" class="nav-item" data-content="invite">
                    <img src="invitation.png" alt="Invitations" class="nav-icon">
                    <span class="nav-text">Invitations</span>
                </a>
            </div>
        </nav>

        <!-- User Menu Section -->
        <div class="sidebar-user-section">
            <!-- User Profile -->
            <div class="user-profile">
                <div class="user-avatar">
                    <img id="user-menu-avatar" src="profile.png" alt="User Avatar" class="avatar-img">
                </div>
                <div class="user-info">
                    <p class="user-name">John Doe</p>
                    <p class="user-company">Acme Inc</p>
                    <div class="user-status">
                        <span class="status-dot"></span>
                        <span class="status-text">Active</span>
                    </div>
                </div>
            </div>

            <!-- User Menu Items -->
            <div class="user-menu-items">
                <a href="#" id="edit-profile" class="user-menu-item">
                    <svg class="menu-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <span class="menu-text">Edit Profile</span>
                </a>

                <!-- Demo Mode Section -->
                <div class="demo-section">
                    <div class="demo-toggle-container">
                        <span id="mode-status" class="demo-label">Live Mode</span>
                        <label class="demo-switch">
                            <input type="checkbox" id="demo-toggle" class="sr-only">
                            <div id="demo-toggle-switch" class="switch-bg">
                                <div class="switch-dot"></div>
                            </div>
                        </label>
                    </div>
                    <p class="demo-description">Switch to demo mode to preview features</p>
                </div>

                <!-- Subscription Section -->
                <div class="subscription-section">
                    <div class="subscription-header">
                        <span class="subscription-label">
                            <svg class="sub-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Subscription:
                        </span>
                        <span class="subscription-type">Free Trial</span>
                    </div>

                    <div id="subscription-end-notice" class="subscription-notice">
                        <svg class="notice-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span id="subscription-end-date">Ends on 12/15/2025 (30 days)</span>
                    </div>

                    <div id="default-subscription-buttons">
                        <button id="manageSubscriptionBtn" class="manage-subscription-btn">
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="btn-text">Manage Subscription</span>
                        </button>
                    </div>
                </div>

                <!-- Credits Section -->
                <div class="credits-section">
                    <div class="credits-header">
                        <span class="credits-label">
                            <svg class="credits-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927C9.435 2.371 10.565 2.371 10.951 2.927L13.361 6.36L17.292 7.043C17.939 7.152 18.19 7.98 17.687 8.434L14.864 10.865L15.589 14.792C15.714 15.438 15.016 15.934 14.434 15.597L10.5 13.377L6.566 15.597C5.984 15.934 5.286 15.438 5.411 14.792L6.136 10.865L3.313 8.434C2.81 7.98 3.061 7.152 3.708 7.043L7.639 6.36L10.049 2.927Z"></path>
                            </svg>
                            Credits:
                        </span>
                        <span class="credits-count">5</span>
                    </div>
                </div>

                <!-- Logout -->
                <a id="user-logout" href="#" class="user-menu-item logout-item">
                    <img src="logout.png" alt="Logout" class="menu-icon">
                    <span class="menu-text">Logout</span>
                </a>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
        <div id="main-content" class="content-wrapper">
            <h1>Sidebar Test Page</h1>
            <p>This page tests the sidebar functionality. Use the debug panel on the right to run tests.</p>
            <div style="height: 1000px; background: linear-gradient(to bottom, #f8fafc, #e2e8f0); padding: 20px; border-radius: 8px;">
                <h2>Scrollable Content</h2>
                <p>This content area should work properly with the sidebar layout.</p>
                <p>The sidebar should remain fixed on the left side.</p>
                <p>Try toggling the sidebar with the toggle button.</p>
                <p>Test the demo mode toggle in the user section.</p>
                <p>Click on navigation items to test functionality.</p>
            </div>
        </div>
    </main>

    <script>
        let debugResults = [];
        
        function addResult(type, message) {
            debugResults.push({ type, message, timestamp: new Date().toLocaleTimeString() });
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const resultsDiv = document.getElementById('debug-results');
            const html = debugResults.map(result => 
                `<div class="debug-item ${result.type}">[${result.timestamp}] ${result.message}</div>`
            ).join('');
            resultsDiv.innerHTML = html;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            debugResults = [];
            updateDebugDisplay();
        }
        
        function testSidebarElements() {
            addResult('info', 'Testing sidebar elements...');
            
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            if (sidebar) addResult('success', '✓ Sidebar element found');
            else addResult('error', '✗ Sidebar element not found');
            
            if (sidebarToggle) addResult('success', '✓ Sidebar toggle button found');
            else addResult('error', '✗ Sidebar toggle button not found');
            
            if (mobileSidebarToggle) addResult('success', '✓ Mobile sidebar toggle found');
            else addResult('error', '✗ Mobile sidebar toggle not found');
            
            if (sidebarOverlay) addResult('success', '✓ Sidebar overlay found');
            else addResult('error', '✗ Sidebar overlay not found');
            
            // Test navigation items
            const navItems = document.querySelectorAll('.nav-item[data-content]');
            addResult('info', `Found ${navItems.length} navigation items`);
            
            if (navItems.length === 5) {
                addResult('success', '✓ All navigation items found');
            } else {
                addResult('warning', `Expected 5 navigation items, found ${navItems.length}`);
            }
            
            // Test if sidebar is visible
            const sidebarRect = sidebar.getBoundingClientRect();
            if (sidebarRect.width > 0 && sidebarRect.height > 0) {
                addResult('success', `✓ Sidebar is visible (${sidebarRect.width}x${sidebarRect.height})`);
            } else {
                addResult('error', '✗ Sidebar is not visible');
            }
        }
        
        function testDemoMode() {
            addResult('info', 'Testing demo mode toggle...');
            
            const demoToggle = document.getElementById('demo-toggle');
            const demoToggleSwitch = document.getElementById('demo-toggle-switch');
            const modeStatus = document.getElementById('mode-status');
            
            if (demoToggle && demoToggleSwitch && modeStatus) {
                addResult('success', '✓ Demo mode elements found');
                
                // Test toggle functionality
                const originalChecked = demoToggle.checked;
                const originalStatus = modeStatus.textContent;
                
                // Simulate a click
                demoToggle.checked = !originalChecked;
                demoToggle.dispatchEvent(new Event('change'));
                
                setTimeout(() => {
                    if (modeStatus.textContent !== originalStatus) {
                        addResult('success', '✓ Demo mode toggle is working');
                    } else {
                        addResult('error', '✗ Demo mode toggle not responding');
                    }
                    
                    // Reset to original state
                    demoToggle.checked = originalChecked;
                    demoToggle.dispatchEvent(new Event('change'));
                }, 100);
                
            } else {
                addResult('error', '✗ Demo mode elements not found');
            }
        }
        
        function testNavigation() {
            addResult('info', 'Testing navigation functionality...');
            
            const navItems = document.querySelectorAll('.nav-item[data-content]');
            
            if (navItems.length > 0) {
                addResult('success', `✓ Found ${navItems.length} navigation items`);
                
                navItems.forEach((item, index) => {
                    const contentType = item.getAttribute('data-content');
                    const navText = item.querySelector('.nav-text')?.textContent || 'Unknown';
                    
                    addResult('info', `Nav item ${index + 1}: ${navText} (${contentType})`);
                    
                    // Test if clicking sets active state
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        addResult('success', `✓ Navigation clicked: ${navText}`);
                    });
                });
                
            } else {
                addResult('error', '✗ No navigation items found');
            }
        }
        
        function testSidebarToggle() {
            addResult('info', 'Testing sidebar toggle functionality...');
            
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            
            if (sidebar && sidebarToggle) {
                const wasCompact = sidebar.classList.contains('compact');
                
                // Simulate toggle
                sidebarToggle.click();
                
                setTimeout(() => {
                    const isCompact = sidebar.classList.contains('compact');
                    if (isCompact !== wasCompact) {
                        addResult('success', '✓ Sidebar toggle is working');
                    } else {
                        addResult('error', '✗ Sidebar toggle not working');
                    }
                }, 100);
                
            } else {
                addResult('error', '✗ Sidebar toggle elements not found');
            }
        }
        
        function runAllTests() {
            clearResults();
            addResult('info', 'Running all tests...');
            
            testSidebarElements();
            setTimeout(() => testDemoMode(), 200);
            setTimeout(() => testNavigation(), 400);
            setTimeout(() => testSidebarToggle(), 600);
            setTimeout(() => addResult('info', 'All tests completed'), 800);
        }
        
        // Set up sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', () => {
                    sidebar.classList.toggle('compact');
                    addResult('info', `Sidebar toggled: ${sidebar.classList.contains('compact') ? 'compact' : 'expanded'}`);
                });
            }
            
            if (mobileSidebarToggle) {
                mobileSidebarToggle.addEventListener('click', () => {
                    sidebar.classList.add('mobile-open');
                    sidebarOverlay.classList.remove('hidden');
                    addResult('info', 'Mobile sidebar opened');
                });
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', () => {
                    sidebar.classList.remove('mobile-open');
                    sidebarOverlay.classList.add('hidden');
                    addResult('info', 'Mobile sidebar closed');
                });
            }
            
            // Set up demo mode toggle
            const demoToggle = document.getElementById('demo-toggle');
            const demoToggleSwitch = document.getElementById('demo-toggle-switch');
            const modeStatus = document.getElementById('mode-status');
            
            if (demoToggle && demoToggleSwitch && modeStatus) {
                demoToggle.addEventListener('change', function() {
                    const dot = demoToggleSwitch.querySelector('.switch-dot');
                    
                    if (this.checked) {
                        demoToggleSwitch.classList.remove('bg-gray-200');
                        demoToggleSwitch.classList.add('bg-blue-600', 'active');
                        if (dot) dot.style.transform = 'translateX(1.25rem)';
                        modeStatus.textContent = 'Demo Mode';
                        addResult('success', 'Demo mode activated');
                    } else {
                        demoToggleSwitch.classList.remove('bg-blue-600', 'active');
                        demoToggleSwitch.classList.add('bg-gray-200');
                        if (dot) dot.style.transform = 'translateX(0)';
                        modeStatus.textContent = 'Live Mode';
                        addResult('success', 'Live mode activated');
                    }
                });
            }
            
            addResult('info', 'Debug dashboard loaded');
        });
    </script>
</body>
</html>
