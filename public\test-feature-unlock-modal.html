<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Unlock Modal Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: #f8fafc;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
            transform: translateY(-1px);
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.375rem;
            background-color: #F3F4F6;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .debug-info {
            background-color: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Feature Unlock Modal Test</h1>
        <p>This page tests the feature unlock modal component for locked dashboard features.</p>
        
        <div class="button-group">
            <button class="test-button" onclick="testDashboardModal()">
                Test Dashboard Modal
            </button>
            
            <button class="test-button" onclick="testAssessmentsModal()">
                Test Assessments Modal
            </button>
            
            <button class="test-button" onclick="testReportsModal()">
                Test Reports Modal
            </button>
            
            <button class="test-button" onclick="testWithEmployees()">
                Test with 1 Employee
            </button>
            
            <button class="test-button" onclick="checkModalAvailability()">
                Check Modal Availability
            </button>
            
            <button class="test-button" onclick="clearResults()">
                Clear Results
            </button>
        </div>
        
        <div id="result" class="result" style="display: none;">
            <strong>Result:</strong> <span id="result-text"></span>
        </div>
        
        <div id="debug-info" class="debug-info" style="display: none;">
            <strong>Debug Information:</strong>
            <pre id="debug-text"></pre>
        </div>
    </div>

    <!-- Include the modal script -->
    <script src="feature-unlock-modal.js"></script>
    
    <script>
        let debugLog = [];
        
        // Override console.log to capture debug information
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            debugLog.push(args.join(' '));
            originalConsoleLog.apply(console, args);
            updateDebugInfo();
        };
        
        function updateDebugInfo() {
            const debugElement = document.getElementById('debug-text');
            if (debugElement) {
                debugElement.textContent = debugLog.slice(-10).join('\n'); // Show last 10 logs
                document.getElementById('debug-info').style.display = 'block';
            }
        }
        
        async function testDashboardModal() {
            try {
                debugLog.push('=== Testing Dashboard Feature Unlock Modal ===');
                updateDebugInfo();
                
                const result = await window.FeatureUnlockModal.show({
                    featureName: 'Dashboard',
                    employeeCount: 0
                });
                
                showResult(`User selected: ${result}`);
                debugLog.push(`Dashboard modal result: ${result}`);
                updateDebugInfo();
            } catch (error) {
                showResult(`Error: ${error.message}`);
                debugLog.push(`Error: ${error.message}`);
                updateDebugInfo();
            }
        }
        
        async function testAssessmentsModal() {
            try {
                debugLog.push('=== Testing Assessments Feature Unlock Modal ===');
                updateDebugInfo();
                
                const result = await window.FeatureUnlockModal.show({
                    featureName: 'Assessments',
                    employeeCount: 0
                });
                
                showResult(`User selected: ${result}`);
                debugLog.push(`Assessments modal result: ${result}`);
                updateDebugInfo();
            } catch (error) {
                showResult(`Error: ${error.message}`);
                debugLog.push(`Error: ${error.message}`);
                updateDebugInfo();
            }
        }
        
        async function testReportsModal() {
            try {
                debugLog.push('=== Testing Reports Feature Unlock Modal ===');
                updateDebugInfo();
                
                const result = await window.FeatureUnlockModal.show({
                    featureName: 'Reports',
                    employeeCount: 0
                });
                
                showResult(`User selected: ${result}`);
                debugLog.push(`Reports modal result: ${result}`);
                updateDebugInfo();
            } catch (error) {
                showResult(`Error: ${error.message}`);
                debugLog.push(`Error: ${error.message}`);
                updateDebugInfo();
            }
        }
        
        async function testWithEmployees() {
            try {
                debugLog.push('=== Testing Modal with 1 Employee ===');
                updateDebugInfo();
                
                const result = await window.FeatureUnlockModal.show({
                    featureName: 'Dashboard',
                    employeeCount: 1
                });
                
                showResult(`User selected: ${result}`);
                debugLog.push(`Modal with employees result: ${result}`);
                updateDebugInfo();
            } catch (error) {
                showResult(`Error: ${error.message}`);
                debugLog.push(`Error: ${error.message}`);
                updateDebugInfo();
            }
        }
        
        function checkModalAvailability() {
            debugLog.push('=== Checking Feature Unlock Modal Availability ===');
            
            const available = typeof window.FeatureUnlockModal !== 'undefined';
            const hasShow = available && typeof window.FeatureUnlockModal.show === 'function';
            
            debugLog.push(`FeatureUnlockModal available: ${available}`);
            debugLog.push(`FeatureUnlockModal.show function: ${hasShow}`);
            
            if (available) {
                debugLog.push(`FeatureUnlockModal methods: ${Object.keys(window.FeatureUnlockModal).join(', ')}`);
            }
            
            showResult(`Modal Available: ${available}, Show Function: ${hasShow}`);
            updateDebugInfo();
        }
        
        function showResult(message) {
            const resultElement = document.getElementById('result-text');
            const resultContainer = document.getElementById('result');
            
            if (resultElement && resultContainer) {
                resultElement.textContent = message;
                resultContainer.style.display = 'block';
            }
        }
        
        function clearResults() {
            debugLog = [];
            document.getElementById('result').style.display = 'none';
            document.getElementById('debug-info').style.display = 'none';
        }
        
        // Initialize debug info on page load
        window.addEventListener('load', function() {
            debugLog.push('Page loaded');
            updateDebugInfo();
            checkModalAvailability();
        });
    </script>
</body>
</html>
